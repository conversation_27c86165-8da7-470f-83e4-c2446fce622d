/* Custom Cursor Styles - Image-based cursors like <PERSON><PERSON><PERSON> */

/* Default cursor for html element */
html {
    cursor: image-set(url("/images/cursors/default.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, default;
    /* Fallback for missing images */
    cursor: default;
}

/* Body and interactive elements */
body {
    cursor: image-set(url("/images/cursors/default.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, default;
    /* Fallback for missing images */
    cursor: default;
}

/* Pointer cursor for interactive elements */
a,
button,
.btn,
input[type="submit"],
input[type="button"],
.cursor-pointer {
    cursor: image-set(url("/images/cursors/pointer.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, pointer !important;
    /* Fallback for missing images */
    cursor: pointer !important;
}

/* Text cursor for input fields */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea,
.cursor-text {
    cursor: image-set(url("/images/cursors/text.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, text !important;
}

/* Grab cursor for draggable elements */
.cursor-grab,
[draggable="true"] {
    cursor: image-set(url("/images/cursors/grab.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, grab !important;
}

/* Grabbing cursor when dragging */
.cursor-grabbing,
[draggable="true"]:active {
    cursor: image-set(url("/images/cursors/grabbing.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, grabbing !important;
}

/* Not allowed cursor */
.cursor-not-allowed,
button:disabled,
.btn:disabled {
    cursor: image-set(url("/images/cursors/not-allowed.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, not-allowed !important;
}

/* Loading cursor */
.cursor-loading,
.loading {
    cursor: image-set(url("/images/cursors/loading.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, wait !important;
}

/* Crosshair cursor */
.cursor-crosshair {
    cursor: image-set(url("/images/cursors/crosshair.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, crosshair !important;
}

/* Help cursor */
.cursor-help {
    cursor: image-set(url("/images/cursors/help.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, help !important;
}

/* Hover effects for better visual feedback */
a:hover,
button:hover,
.btn:hover {
    cursor: image-set(url("/images/cursors/pointer-hover.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, pointer !important;
}

/* Active/Click states */
a:active,
button:active,
.btn:active {
    cursor: image-set(url("/images/cursors/pointer-active.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, pointer !important;
}

/* Focus states for form elements */
input:focus,
textarea:focus,
select:focus {
    cursor: image-set(url("/images/cursors/text-focus.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, text !important;
}

/* Resize cursors */
.cursor-resize-n {
    cursor: image-set(url("/images/cursors/resize-n.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, n-resize !important;
}

.cursor-resize-s {
    cursor: image-set(url("/images/cursors/resize-s.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, s-resize !important;
}

.cursor-resize-e {
    cursor: image-set(url("/images/cursors/resize-e.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, e-resize !important;
}

.cursor-resize-w {
    cursor: image-set(url("/images/cursors/resize-w.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, w-resize !important;
}

.cursor-resize-ne {
    cursor: image-set(url("/images/cursors/resize-ne.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, ne-resize !important;
}

.cursor-resize-nw {
    cursor: image-set(url("/images/cursors/resize-nw.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, nw-resize !important;
}

.cursor-resize-se {
    cursor: image-set(url("/images/cursors/resize-se.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, se-resize !important;
}

.cursor-resize-sw {
    cursor: image-set(url("/images/cursors/resize-sw.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, sw-resize !important;
}

/* Mobile and touch device handling */
@media (max-width: 768px), (hover: none) {
    html,
    body,
    a,
    button,
    .btn,
    input,
    textarea,
    select {
        cursor: auto !important;
    }
}

/* High DPI display support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    html {
        cursor: url("/images/cursors/<EMAIL>") 16 16, default;
    }

    a, button, .btn {
        cursor: url("/images/cursors/<EMAIL>") 16 16, pointer !important;
    }

    input[type="text"], textarea {
        cursor: url("/images/cursors/<EMAIL>") 16 16, text !important;
    }
}

/* Fallback for browsers that don't support image-set */
@supports not (cursor: image-set(url("test.png") 1x)) {
    html {
        cursor: url("/images/cursors/default.png") 8 8, default;
    }

    a, button, .btn {
        cursor: url("/images/cursors/pointer.png") 8 8, pointer !important;
    }

    input[type="text"], textarea {
        cursor: url("/images/cursors/text.png") 8 8, text !important;
    }

    .cursor-loading {
        cursor: url("/images/cursors/loading.png") 8 8, wait !important;
    }

    .cursor-not-allowed, button:disabled {
        cursor: url("/images/cursors/not-allowed.png") 8 8, not-allowed !important;
    }
}

/* Smooth cursor transitions */
* {
    transition: cursor 0.1s ease;
}

/* Custom cursor classes for specific use cases */
.cursor-zoom-in {
    cursor: image-set(url("/images/cursors/zoom-in.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, zoom-in !important;
}

.cursor-zoom-out {
    cursor: image-set(url("/images/cursors/zoom-out.png") 1x, url("/images/cursors/<EMAIL>") 2x) 8 8, zoom-out !important;
}
