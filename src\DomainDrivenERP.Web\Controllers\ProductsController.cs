using DomainDrivenERP.Web.Models.Products;
using DomainDrivenERP.Web.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace DomainDrivenERP.Web.Controllers;

[Authorize]
public class ProductsController : Controller
{
    private readonly IApiService _apiService;
    private readonly IAuthService _authService;
    private readonly ILogger<ProductsController> _logger;

    public ProductsController(IApiService apiService, IAuthService authService, ILogger<ProductsController> logger)
    {
        _apiService = apiService;
        _authService = authService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        await SetAuthorizationToken();
        
        // For now, return empty list - we'll implement GetAll products later
        var products = new List<Product>();
        return View(products);
    }

    public async Task<IActionResult> Details(Guid id)
    {
        await SetAuthorizationToken();
        
        var response = await _apiService.GetAsync<Product>($"api/v1/products/{id}");
        
        if (!response.IsSuccess)
        {
            TempData["ErrorMessage"] = response.ErrorMessage;
            return RedirectToAction(nameof(Index));
        }

        return View(response.Data);
    }

    public IActionResult Create()
    {
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(AddProductRequest model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        await SetAuthorizationToken();

        var command = new
        {
            CategoryId = model.CategoryId,
            ProductId = Guid.NewGuid(),
            ProductName = model.ProductName,
            Amount = model.Amount,
            Currency = model.Currency,
            StockQuantity = model.StockQuantity,
            Sku = model.Sku,
            Model = model.Model,
            Details = model.Details
        };

        var response = await _apiService.PostAsync<Product>("api/v1/products/add", command);

        if (response.IsSuccess)
        {
            TempData["SuccessMessage"] = "Product created successfully!";
            return RedirectToAction(nameof(Index));
        }

        foreach (var error in response.Errors)
        {
            ModelState.AddModelError(string.Empty, error);
        }

        return View(model);
    }

    public async Task<IActionResult> Edit(Guid id)
    {
        await SetAuthorizationToken();
        
        var response = await _apiService.GetAsync<Product>($"api/v1/products/{id}");
        
        if (!response.IsSuccess)
        {
            TempData["ErrorMessage"] = response.ErrorMessage;
            return RedirectToAction(nameof(Index));
        }

        var model = new AddProductRequest
        {
            ProductName = response.Data!.Name,
            Amount = response.Data.Amount,
            Currency = response.Data.Currency,
            StockQuantity = response.Data.StockQuantity,
            Sku = response.Data.Sku,
            Model = response.Data.Model,
            Details = response.Data.Details,
            CategoryId = response.Data.CategoryId
        };

        ViewData["ProductId"] = id;
        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(Guid id, AddProductRequest model)
    {
        if (!ModelState.IsValid)
        {
            ViewData["ProductId"] = id;
            return View(model);
        }

        await SetAuthorizationToken();

        // Update product name
        var updateNameCommand = new { ProductId = id, NewName = model.ProductName };
        var nameResponse = await _apiService.PutAsync<bool>("api/v1/products/update-name", updateNameCommand);

        // Update product price
        var updatePriceCommand = new { ProductId = id, NewPrice = model.Amount };
        var priceResponse = await _apiService.PutAsync<bool>("api/v1/products/update-price", updatePriceCommand);

        if (nameResponse.IsSuccess && priceResponse.IsSuccess)
        {
            TempData["SuccessMessage"] = "Product updated successfully!";
            return RedirectToAction(nameof(Index));
        }

        var errors = nameResponse.Errors.Concat(priceResponse.Errors);
        foreach (var error in errors)
        {
            ModelState.AddModelError(string.Empty, error);
        }

        ViewData["ProductId"] = id;
        return View(model);
    }

    private async Task SetAuthorizationToken()
    {
        var token = await _authService.GetTokenAsync();
        if (!string.IsNullOrEmpty(token))
        {
            _apiService.SetAuthorizationToken(token);
        }
    }
}
