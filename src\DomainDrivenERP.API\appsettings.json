{
  "ConnectionStrings": {
    //"SqlServer": "Server=sqlserver;Database=ERPSystemDB;User=sa;Password=***********;;MultipleActiveResultSets=true;TrustServerCertificate=true;",
    "SqlServer": "Server=.;Database=ERPSystemDB;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=true;",
    //"SqlServer": "Server=A7MD\\A7MD;Database=ERPSystemDB;User Id=sa;Password=***********;MultipleActiveResultSets=true;TrustServerCertificate=true;",
    //"SqlServer": "Server=localhost,14330;Database=CleanDDD;User Id=sa;Password=***********;MultipleActiveResultSets=true;TrustServerCertificate=true;", //For Updating Database
    "Redis": "localhost:6379",
    "IdentityConnection": "Server=.;Database=ERPSystemIdentityDB;Integrated Security=true;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=true;"
    //"IdentityConnection": "Server=sqlserver;Database=ERPSystemDB_Identity;User Id=sa;Password=***********;MultipleActiveResultSets=true;TrustServerCertificate=true;"
  },
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console", "Serilog.Sinks.File" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      { "Name": "Console" },
      {
        "Name": "File",
        "Args": {
          "path": "/app/logs/log-.txt",
          "rollingInterval": "Day",
          "rollOnFileSizeLimit": true,
          "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"
        }
      }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ]
  },
  "AllowedHosts": "*",
  "JwtSettings": {
    "Key": "sz8eI7OdHBrjrIo8j9nTW/rQyO1OvY0pAQ2wDKQZw/0=",
    "Issuer": "https://localhost:7124/",
    "Audience": "https://localhost:7124/",
    "DurationInDays": 30
  },
  "emailSettings": {
    "port": "465",
    "host": "smtp.yandex.com", //smtp.office365.com
    "FromEmail": "<EMAIL>",
    "password": "jgcjkohvcspxbrdy"

  },
  "IdentityDefaultOptions": {
    "PasswordRequireDigit": false,
    "PasswordRequiredLength": 3,
    "PasswordRequireNonAlphanumeric": false,
    "PasswordRequireUppercase": false,
    "PasswordRequireLowercase": false,
    "PasswordRequiredUniqueChars": 0,
    "LockoutDefaultLockoutTimeSpanInMinutes": 30,
    "LockoutMaxFailedAccessAttempts": 5,
    "LockoutAllowedForNewUsers": false,
    "UserRequireUniqueEmail": true,
    "SignInRequireConfirmedEmail": false
  },
  "LocalizationSettings": {
    "DefaultLanguageCode": "en",
    "FallbackLanguageCode": "en",
    "AllowUserLanguageSelection": true,
    "AutoDetectLanguage": true,
    "ShowLanguageSelector": true,
    "LoadAllLanguagesOnStartup": true,
    "CacheTranslations": true,
    "CacheExpirationMinutes": 60,
    "ResourceFilePath": "Resources/Translations"
  }
}
