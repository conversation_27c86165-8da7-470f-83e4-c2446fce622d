using DomainDrivenERP.Application.Abstractions.Messaging;
using DomainDrivenERP.Domain.Abstractions.Identity;
using DomainDrivenERP.Domain.Shared.Results;

namespace DomainDrivenERP.Application.Features.Roles.Queries.GetAllClaims;
internal class GetAllClaimsQueryHandler : IListQueryHandler<GetAllClaimsQuery, string>
{
    private readonly IRoleService _roleService;

    public GetAllClaimsQueryHandler(IRoleService roleService)
    {
        _roleService = roleService;
    }
    public async Task<Result<CustomList<string>>> Handle(GetAllClaimsQuery request, CancellationToken cancellationToken)
    {
        return await _roleService.GetAllClaimsAsync(cancellationToken);
    }
}
