﻿namespace DomainDrivenERP.Identity.Models;
public class DefaultIdentityOptions
{
    //password settings
    public int Id { get; set; }
    public bool PasswordRequireDigit { get; set; }
    public int PasswordRequiredLength { get; set; }
    public bool PasswordRequireNonAlphanumeric { get; set; }
    public bool PasswordRequireUppercase { get; set; }
    public bool PasswordRequireLowercase { get; set; }
    public int PasswordRequiredUniqueChars { get; set; }

    //lockout settings
    public double LockoutDefaultLockoutTimeSpanInMinutes { get; set; }
    public int LockoutMaxFailedAccessAttempts { get; set; }
    public bool LockoutAllowedForNewUsers { get; set; }

    //user settings
    public bool UserRequireUniqueEmail { get; set; }
    public bool SignInRequireConfirmedEmail { get; set; }
}
