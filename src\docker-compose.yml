version: '3.4'

services:
  DomainDrivenERP.api:
    image: ${DOCKER_REGISTRY-}cleanarchitecturewithdddapi
    container_name: Api
    build:
      context: .
      dockerfile: DomainDrivenERP.API/Dockerfile
    ports:
      - "8080:8080"
      - "8081:8081"
    depends_on:
      - redis
      - sqlserver
      - seq
    networks:
      - local-testing

  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    restart: always
    networks:
      - local-testing
    volumes:
      - ./logs:/app/logs

  sqlserver:
    image: mcr.microsoft.com/mssql/server
    container_name: sqlserver
    environment:
      SA_PASSWORD: "P@ssw0rd123"
      ACCEPT_EULA: "Y"
      MSSQL_PID: "Developer"
    ports:
      - "14330:1433"
    volumes:
      - ./containers/sqlserver-data:/var/opt/mssql/data 
    networks:
      - local-testing
  
  seq:
    image: datalust/seq:latest
    container_name: seq
    ports:
      - "5341:80"
      - "5342:80"
    restart: always
    environment:
      ACCEPT_EULA: "Y"
    networks:
      - local-testing

networks:
  local-testing:
    name: local-testing

x-docker-project-name: CleanDDD
