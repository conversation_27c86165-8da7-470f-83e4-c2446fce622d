﻿@{
    ViewData["Title"] = "Welcome";
}

<div class="container-fluid px-0">
    <!-- Hero Section -->
    <section class="text-center py-5 mb-5" style="background: var(--gradient-surface); border-radius: var(--radius-2xl); margin: 0 var(--spacing-md);">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="mb-4">
                        <span class="badge" style="background: var(--brand-primary-light); color: var(--brand-primary); padding: var(--spacing-sm) var(--spacing-lg); border-radius: var(--radius-xl); font-weight: 600; font-size: 0.875rem;">
                            ✨ Modern ERP Solution
                        </span>
                    </div>

                    <h1 class="mb-4" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 800; line-height: 1.1;">
                        Domain Driven ERP
                    </h1>

                    <p class="lead mb-5" style="color: var(--text-secondary); font-size: 1.25rem; max-width: 600px; margin: 0 auto;">
                        Experience the future of enterprise resource planning with our clean, modern, and intuitive platform built for the digital age.
                    </p>

                    @if (!User.Identity.IsAuthenticated)
                    {
                        <div class="d-flex flex-column flex-sm-row justify-content-center gap-3 mb-4">
                            <a asp-controller="Auth" asp-action="Register" class="btn btn-primary btn-lg">
                                <i class="fas fa-rocket"></i>
                                Get Started Free
                            </a>
                            <a asp-controller="Auth" asp-action="Login" class="btn btn-outline btn-lg">
                                <i class="fas fa-sign-in-alt"></i>
                                Sign In
                            </a>
                        </div>

                        <p class="small" style="color: var(--text-muted);">
                            No credit card required • Free forever plan available
                        </p>
                    }
                    else
                    {
                        <div class="d-flex justify-content-center gap-3">
                            <a asp-controller="Products" asp-action="Index" class="btn btn-primary btn-lg">
                                <i class="fas fa-tachometer-alt"></i>
                                Go to Dashboard
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="container mb-5">
        @if (User.Identity.IsAuthenticated)
        {
            <div class="text-center mb-5">
                <h2 class="mb-3">Your Dashboard</h2>
                <p class="lead" style="color: var(--text-secondary);">Quick access to your most important business tools</p>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; background: var(--brand-primary-light); border-radius: var(--radius-xl);">
                                    <i class="fas fa-box fa-2x" style="color: var(--brand-primary);"></i>
                                </div>
                            </div>
                            <h5 class="card-title mb-3">Products</h5>
                            <p class="card-text mb-4" style="color: var(--text-secondary);">
                                Manage your product catalog with advanced inventory tracking and analytics
                            </p>
                            <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i>
                                View Products
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; background: rgba(5, 150, 105, 0.1); border-radius: var(--radius-xl);">
                                    <i class="fas fa-shopping-cart fa-2x" style="color: var(--brand-success);"></i>
                                </div>
                            </div>
                            <h5 class="card-title mb-3">Orders</h5>
                            <p class="card-text mb-4" style="color: var(--text-secondary);">
                                Track and manage customer orders with real-time status updates
                            </p>
                            <a asp-controller="Orders" asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i>
                                View Orders
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; background: rgba(217, 119, 6, 0.1); border-radius: var(--radius-xl);">
                                    <i class="fas fa-users fa-2x" style="color: var(--brand-warning);"></i>
                                </div>
                            </div>
                            <h5 class="card-title mb-3">Customers</h5>
                            <p class="card-text mb-4" style="color: var(--text-secondary);">
                                Build and maintain strong customer relationships with CRM tools
                            </p>
                            <a asp-controller="Customers" asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i>
                                View Customers
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="text-center mb-5">
                <h2 class="mb-3">Why Choose Our Platform?</h2>
                <p class="lead" style="color: var(--text-secondary);">Built for modern businesses with cutting-edge technology</p>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 text-center glass">
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; background: var(--brand-primary-light); border-radius: var(--radius-xl);">
                                    <i class="fas fa-rocket fa-2x" style="color: var(--brand-primary);"></i>
                                </div>
                            </div>
                            <h5 class="card-title mb-3">Lightning Fast</h5>
                            <p class="card-text" style="color: var(--text-secondary);">
                                Built with modern architecture for blazing fast performance and real-time updates
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 text-center glass">
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; background: rgba(5, 150, 105, 0.1); border-radius: var(--radius-xl);">
                                    <i class="fas fa-mobile-alt fa-2x" style="color: var(--brand-success);"></i>
                                </div>
                            </div>
                            <h5 class="card-title mb-3">Mobile First</h5>
                            <p class="card-text" style="color: var(--text-secondary);">
                                Responsive design that works perfectly on desktop, tablet, and mobile devices
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 text-center glass">
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; background: rgba(220, 38, 38, 0.1); border-radius: var(--radius-xl);">
                                    <i class="fas fa-shield-alt fa-2x" style="color: var(--brand-danger);"></i>
                                </div>
                            </div>
                            <h5 class="card-title mb-3">Enterprise Security</h5>
                            <p class="card-text" style="color: var(--text-secondary);">
                                Bank-level security with encryption, audit trails, and compliance features
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        }
    </section>

    <!-- Theme Demo Section -->
    <section class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center p-5" style="background: var(--gradient-surface); border-radius: var(--radius-2xl); border: 1px solid var(--border-light);">
                    <div class="mb-4">
                        <div class="d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px; background: var(--brand-primary-light); border-radius: var(--radius-xl);">
                            <i class="fas fa-palette fa-lg" style="color: var(--brand-primary);"></i>
                        </div>
                    </div>

                    <h3 class="mb-3">Dark & Light Mode</h3>
                    <p class="mb-4" style="color: var(--text-secondary);">
                        Experience our beautiful interface in both light and dark themes. Toggle instantly with the switch in the navigation bar.
                    </p>

                    <div class="d-flex flex-column flex-sm-row align-items-center justify-content-center gap-3 mb-4">
                        <div class="theme-toggle" onclick="window.themeSwitcher?.toggleTheme()"></div>
                        <span class="small" style="color: var(--text-muted);">
                            Or press <kbd style="background: var(--bg-tertiary); padding: 2px 6px; border-radius: 4px; font-size: 0.75rem;">Ctrl+Shift+T</kbd>
                        </span>
                    </div>

                    <p class="small mb-0" style="color: var(--text-muted);">
                        Your preference is automatically saved and synced across devices
                    </p>
                </div>
            </div>
        </div>
    </section>
</div>
