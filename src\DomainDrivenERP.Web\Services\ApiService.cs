using System.Text;
using DomainDrivenERP.Web.Configuration;
using DomainDrivenERP.Web.Models.Common;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace DomainDrivenERP.Web.Services;

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ApiSettings _apiSettings;
    private readonly ILogger<ApiService> _logger;

    public ApiService(HttpClient httpClient, IOptions<ApiSettings> apiSettings, ILogger<ApiService> logger)
    {
        _httpClient = httpClient;
        _apiSettings = apiSettings.Value;
        _logger = logger;
        
        _httpClient.BaseAddress = new Uri(_apiSettings.BaseUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(_apiSettings.Timeout);
    }

    public async Task<ApiResponse<T>> GetAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.GetAsync(endpoint);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while making GET request to {Endpoint}", endpoint);
            return ApiResponse<T>.Failure("An error occurred while processing your request.");
        }
    }

    public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object data)
    {
        try
        {
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync(endpoint, content);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while making POST request to {Endpoint}", endpoint);
            return ApiResponse<T>.Failure("An error occurred while processing your request.");
        }
    }

    public async Task<ApiResponse<T>> PutAsync<T>(string endpoint, object data)
    {
        try
        {
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync(endpoint, content);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while making PUT request to {Endpoint}", endpoint);
            return ApiResponse<T>.Failure("An error occurred while processing your request.");
        }
    }

    public async Task<ApiResponse<T>> DeleteAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.DeleteAsync(endpoint);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while making DELETE request to {Endpoint}", endpoint);
            return ApiResponse<T>.Failure("An error occurred while processing your request.");
        }
    }

    public void SetAuthorizationToken(string token)
    {
        _httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
    }

    public void ClearAuthorizationToken()
    {
        _httpClient.DefaultRequestHeaders.Authorization = null;
    }

    private async Task<ApiResponse<T>> ProcessResponse<T>(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();
        
        if (response.IsSuccessStatusCode)
        {
            try
            {
                var data = JsonConvert.DeserializeObject<T>(content);
                return ApiResponse<T>.Success(data);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error deserializing response: {Content}", content);
                return ApiResponse<T>.Failure("Invalid response format.");
            }
        }
        else
        {
            _logger.LogWarning("API request failed with status {StatusCode}: {Content}", 
                response.StatusCode, content);
            return ApiResponse<T>.Failure($"Request failed: {response.StatusCode}");
        }
    }
}
