root = true

# C# files
[*.cs]

#### Core EditorConfig Options ####

# Indentation and spacing
indent_size = 4
indent_style = space
tab_width = 4

# New line preferences
end_of_line = crlf
insert_final_newline = true

#### .NET Coding Conventions ####

# Organize usings
dotnet_separate_import_directive_groups = false
dotnet_sort_system_directives_first = true

# this. and Me. preferences
dotnet_style_qualification_for_event = false:error
dotnet_style_qualification_for_field = false:error
dotnet_style_qualification_for_method = false:error
dotnet_style_qualification_for_property = false:error

# Language keywords vs BCL types preferences
dotnet_style_predefined_type_for_locals_parameters_members = true:error
dotnet_style_predefined_type_for_member_access = true:error

# Parentheses preferences
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_other_binary_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary:error

# Modifier preferences
dotnet_style_require_accessibility_modifiers = for_non_interface_members:error

# Expression-level preferences
dotnet_style_coalesce_expression = true:error
dotnet_style_collection_initializer = true:error
dotnet_style_explicit_tuple_names = true:error
dotnet_style_null_propagation = true:error
dotnet_style_object_initializer = true:error
dotnet_style_prefer_auto_properties = true:warning
dotnet_style_prefer_compound_assignment = true:error
dotnet_style_prefer_conditional_expression_over_assignment = true:error
dotnet_style_prefer_conditional_expression_over_return = true:error
dotnet_style_prefer_inferred_anonymous_type_member_names = true:error
dotnet_style_prefer_inferred_tuple_names = true:error
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:error

# Field preferences
dotnet_style_readonly_field = true:error

# Parameter preferences
dotnet_code_quality_unused_parameters = all:error

#### C# Coding Conventions ####

# Namespace preferences
csharp_style_namespace_declarations= file_scoped:error

# var preferences
csharp_style_var_elsewhere = false:error
csharp_style_var_for_built_in_types = false:error
csharp_style_var_when_type_is_apparent = true:error

# Expression-bodied members
csharp_style_expression_bodied_accessors = true:error
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_indexers = true:error
csharp_style_expression_bodied_lambdas = true:error
csharp_style_expression_bodied_local_functions = true:error
csharp_style_expression_bodied_methods = false:silent
csharp_style_expression_bodied_operators = true:none
csharp_style_expression_bodied_properties = true:error

# Pattern matching preferences
csharp_style_pattern_matching_over_as_with_null_check = true:error
csharp_style_pattern_matching_over_is_with_cast_check = true:error
csharp_style_prefer_switch_expression = true:error

# Null-checking preferences
csharp_style_conditional_delegate_call = true:error

# Modifier preferences
csharp_prefer_static_local_function = true:error
csharp_preferred_modifier_order = public,private,protected,internal,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async

# Code-block preferences
csharp_prefer_braces = true:none
csharp_prefer_simple_using_statement = true:error

# Expression-level preferences
csharp_prefer_simple_default_expression = true:error
csharp_style_deconstructed_variable_declaration = true:suggestion
csharp_style_inlined_variable_declaration = true:error
csharp_style_pattern_local_over_anonymous_function = true:error
csharp_style_prefer_index_operator = true:suggestion
csharp_style_prefer_range_operator = true:suggestion
csharp_style_throw_expression = true:suggestion
csharp_style_unused_value_assignment_preference = discard_variable:silent
csharp_style_unused_value_expression_statement_preference = discard_variable:silent

# 'using' directive preferences
csharp_using_directive_placement = outside_namespace:error

#### C# Formatting Rules ####

# New line preferences
csharp_new_line_before_catch =true
csharp_new_line_before_else =true
csharp_new_line_before_finally =true
csharp_new_line_before_members_in_anonymous_types = false,
csharp_new_line_before_members_in_object_initializers = false,
csharp_new_line_before_open_brace = all
csharp_new_line_between_query_expression_clauses = true

# Indentation preferences
csharp_indent_block_contents = true
csharp_indent_braces = false
csharp_indent_case_contents = true
csharp_indent_case_contents_when_block = true
csharp_indent_labels = one_less_than_current
csharp_indent_switch_labels = true

# Space preferences
csharp_space_after_cast = false
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_after_comma = true
csharp_space_after_dot = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_after_semicolon_in_for_statement = true
csharp_space_around_binary_operators = before_and_after
csharp_space_around_declaration_statements = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_before_comma = false
csharp_space_before_dot = false
csharp_space_before_open_square_brackets = false
csharp_space_before_semicolon_in_for_statement = false
csharp_space_between_empty_square_brackets = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_declaration_name_and_open_parenthesis = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_between_square_brackets = false

# Wrapping preferences
csharp_preserve_single_line_blocks = true
csharp_preserve_single_line_statements = false

#### Naming styles ####

# Naming rules

dotnet_naming_rule.interface_should_be_begins_with_i.severity = suggestion
dotnet_naming_rule.interface_should_be_begins_with_i.symbols = interface
dotnet_naming_rule.interface_should_be_begins_with_i.style = begins_with_i

dotnet_naming_rule.types_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case

# Symbol specifications

dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.interface.required_modifiers = 

dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.types.required_modifiers = 

dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.non_field_members.required_modifiers = 

# Naming styles

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

dotnet_naming_style.begins_with_i.required_prefix = I
dotnet_naming_style.begins_with_i.required_suffix = 
dotnet_naming_style.begins_with_i.word_separator = 
dotnet_naming_style.begins_with_i.capitalization = pascal_case

# StyleCop.Analyzers

# SA0001: XML comment analysis is disabled due to project configuration
dotnet_diagnostic.SA0001.severity = none

# SA1101: Prefix local calls with this
dotnet_diagnostic.SA1101.severity = none

# SA1309: Field names should not begin with underscore
dotnet_diagnostic.SA1309.severity = none

# SA1413: Use trailing comma in multi-line initializers
dotnet_diagnostic.SA1413.severity = none

# SA1600: Elements should be documented
dotnet_diagnostic.SA1600.severity = none

# SA1633: File should have header
dotnet_diagnostic.SA1633.severity = none

# SA1649: File name should match first type name
dotnet_diagnostic.SA1649.severity = none

# SonarAnalyzers.CSharp

# S1121: Assignments should not be made from within sub-expressions
dotnet_diagnostic.S1121.severity = none

# NET.CodeAnalyzers

# CA1000: Do not declare static members on generic types
dotnet_diagnostic.CA1000.severity = none

# CA1014: Mark assemblies with CLSCompliantAttribute
dotnet_diagnostic.CA1014.severity = none

# CA1030: Use events where appropriate
dotnet_diagnostic.CA1030.severity = none

# CA1031: Do not catch general exception types
dotnet_diagnostic.CA1031.severity = none

# CA1040: Avoid empty interfaces
dotnet_diagnostic.CA1040.severity = none

# CA1062: Validate arguments of public methods
dotnet_diagnostic.CA1062.severity = none

# CA1711: Identifiers should not have incorrect suffix
dotnet_diagnostic.CA1711.severity = none

# CA1716: Identifiers should not match keywords
dotnet_diagnostic.CA1716.severity = none

# CA1724: Type names should not match namespaces
dotnet_diagnostic.CA1724.severity = none

# CA2326: Avoid uninstantiated internal classes
dotnet_diagnostic.CA1812.severity = none

# CA1819: Properties should not return arrays
dotnet_diagnostic.CA1819.severity = none

# CA1848: Use the LoggerMessage delegates
dotnet_diagnostic.CA1848.severity = none

# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = none

# CA2225: Operator overloads have named alternates
dotnet_diagnostic.CA2225.severity = none

# CA2326: Do not use TypeNameHandling values other than None
dotnet_diagnostic.CA2326.severity = none

# CA2327: Do not use insecure JsonSerializerSettings
dotnet_diagnostic.CA2327.severity = none

# Visual Studio.Analyzers

# IDE0046: Convert to conditional expression
dotnet_diagnostic.IDE0046.severity = none
dotnet_diagnostic.IDE0011.severity = none
csharp_style_prefer_method_group_conversion = true:silent
csharp_style_prefer_top_level_statements = true:silent
csharp_style_prefer_primary_constructors = true:suggestion
csharp_style_allow_embedded_statements_on_same_line_experimental = true:silent
csharp_style_prefer_null_check_over_type_check = true:suggestion
csharp_style_prefer_local_over_anonymous_function = true:suggestion
csharp_style_implicit_object_creation_when_type_is_apparent = true:suggestion
csharp_style_prefer_tuple_swap = true:suggestion
csharp_style_prefer_utf8_string_literals = true:suggestion
csharp_style_prefer_readonly_struct_member = true:suggestion
csharp_style_prefer_readonly_struct = true:suggestion
csharp_style_allow_blank_lines_between_consecutive_braces_experimental = true:silent
csharp_style_allow_blank_line_after_colon_in_constructor_initializer_experimental = true:silent
csharp_style_allow_blank_line_after_token_in_conditional_expression_experimental = true:silent
csharp_style_allow_blank_line_after_token_in_arrow_expression_clause_experimental = true:silent
csharp_style_prefer_pattern_matching = true:silent
csharp_style_prefer_extended_property_pattern = true:suggestion
csharp_style_prefer_not_pattern = true:suggestion

# SA1508: Closing braces should not be preceded by blank line
dotnet_diagnostic.SA1508.severity = none

# SA1513: Closing brace should be followed by blank line
dotnet_diagnostic.SA1513.severity = none

# SA1516: Elements should be separated by blank line
dotnet_diagnostic.SA1516.severity = none

# IDE0024: Use expression body for operator
dotnet_diagnostic.IDE0024.severity = none

[*.{cs,vb}]
dotnet_style_coalesce_expression = true:error
dotnet_style_null_propagation = true:error
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:error
dotnet_style_prefer_auto_properties = true:warning
dotnet_style_object_initializer = true:error
dotnet_style_collection_initializer = true:error
dotnet_style_prefer_simplified_boolean_expressions = true:suggestion
dotnet_style_prefer_conditional_expression_over_assignment = true:error
dotnet_style_prefer_conditional_expression_over_return = true:none
dotnet_style_explicit_tuple_names = true:error
dotnet_style_prefer_inferred_tuple_names = true:error
dotnet_style_prefer_inferred_anonymous_type_member_names = true:error
dotnet_style_prefer_compound_assignment = true:error
dotnet_style_prefer_simplified_interpolation = true:suggestion
dotnet_style_namespace_match_folder = true:suggestion
dotnet_style_operator_placement_when_wrapping = beginning_of_line
tab_width = 4
indent_size = 4
end_of_line = crlf
dotnet_style_allow_statement_immediately_after_block_experimental = true:silent
dotnet_style_prefer_collection_expression = when_types_loosely_match:suggestion
dotnet_style_readonly_field = true:error
dotnet_style_predefined_type_for_locals_parameters_members = true:error
dotnet_style_predefined_type_for_member_access = true:error
dotnet_style_require_accessibility_modifiers = for_non_interface_members:error
dotnet_style_allow_multiple_blank_lines_experimental = true:silent
dotnet_code_quality_unused_parameters = all:error
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_other_binary_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary:error
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:error
dotnet_style_qualification_for_field = false:error
dotnet_style_qualification_for_property = false:error
dotnet_style_qualification_for_method = false:error
dotnet_style_qualification_for_event = false:error