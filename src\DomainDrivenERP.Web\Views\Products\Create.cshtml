@model DomainDrivenERP.Web.Models.Products.AddProductRequest
@{
    ViewData["Title"] = "Create Product";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3>Create New Product</h3>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="All" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ProductName" class="form-label"></label>
                                <input asp-for="ProductName" class="form-control" placeholder="Enter product name" />
                                <span asp-validation-for="ProductName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Sku" class="form-label"></label>
                                <input asp-for="Sku" class="form-control" placeholder="Enter SKU" />
                                <span asp-validation-for="Sku" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label asp-for="Amount" class="form-label"></label>
                                <input asp-for="Amount" class="form-control" placeholder="0.00" step="0.01" />
                                <span asp-validation-for="Amount" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label asp-for="Currency" class="form-label"></label>
                                <select asp-for="Currency" class="form-control">
                                    <option value="USD">USD</option>
                                    <option value="EUR">EUR</option>
                                    <option value="GBP">GBP</option>
                                </select>
                                <span asp-validation-for="Currency" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label asp-for="StockQuantity" class="form-label"></label>
                                <input asp-for="StockQuantity" class="form-control" placeholder="0" />
                                <span asp-validation-for="StockQuantity" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Model" class="form-label"></label>
                                <input asp-for="Model" class="form-control" placeholder="Enter model" />
                                <span asp-validation-for="Model" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="CategoryId" class="form-label"></label>
                                <input asp-for="CategoryId" class="form-control" placeholder="Enter category ID" />
                                <span asp-validation-for="CategoryId" class="text-danger"></span>
                                <small class="form-text text-muted">Note: Category selection will be improved in future updates</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Details" class="form-label"></label>
                        <textarea asp-for="Details" class="form-control" rows="3" placeholder="Enter product details"></textarea>
                        <span asp-validation-for="Details" class="text-danger"></span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
