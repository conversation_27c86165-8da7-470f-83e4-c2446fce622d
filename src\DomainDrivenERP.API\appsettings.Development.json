{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "/app/logs/log-.txt", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.JsonFormatter"}}, {"Name": "Seq", "Args": {"serverUrl": "http://seq:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}