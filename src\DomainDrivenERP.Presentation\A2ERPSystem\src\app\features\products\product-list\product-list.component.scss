@import '../../../shared/styles/base';

.product-table-container {
  margin-top: $spacing-unit * 4;
  overflow-x: auto;
  background: $background;
  border-radius: $border-radius;
  box-shadow: var(--tui-shadow);
}

table {
  width: 100%;
  border-collapse: collapse;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-unit * 8 0;
  text-align: center;

  i {
    font-size: 3rem;
    color: $text-secondary;
    margin-bottom: $spacing-unit * 4;
  }

  h3 {
    margin: 0 0 $spacing-unit * 2;
    color: $text-color;
  }

  p {
    margin: 0 0 $spacing-unit * 4;
    color: $text-secondary;
  }
}

.alert {
  display: flex;
  align-items: center;
  padding: $spacing-unit * 4;
  border-radius: $border-radius;
  
  &-error {
    background: var(--tui-error-bg);
    color: var(--tui-error-fill);
  }
  
  i {
    margin-right: $spacing-unit * 2;
    font-size: 1.25rem;
  }
}

.status-badge {
  display: inline-block;
  padding: $spacing-unit $spacing-unit * 2;
  border-radius: $border-radius;
  font-size: 0.875rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .product-table-container {
    margin-top: $spacing-unit * 2;
  }
}