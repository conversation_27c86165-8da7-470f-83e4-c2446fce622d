@model DomainDrivenERP.Web.Models.Auth.RegisterRequest
@{
    ViewData["Title"] = "Register";
}

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">Register</h3>
            </div>
            <div class="card-body">
                <form asp-action="Register" method="post">
                    <div asp-validation-summary="All" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="FirstName" class="form-label"></label>
                                <input asp-for="FirstName" class="form-control" placeholder="Enter your first name" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="LastName" class="form-label"></label>
                                <input asp-for="LastName" class="form-control" placeholder="Enter your last name" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Email" class="form-label"></label>
                        <input asp-for="Email" class="form-control" placeholder="Enter your email" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="UserName" class="form-label"></label>
                        <input asp-for="UserName" class="form-control" placeholder="Enter your username" />
                        <span asp-validation-for="UserName" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="Password" class="form-label"></label>
                        <input asp-for="Password" class="form-control" placeholder="Enter your password" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label asp-for="ConfirmPassword" class="form-label"></label>
                        <input asp-for="ConfirmPassword" class="form-control" placeholder="Confirm your password" />
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Register</button>
                    </div>
                </form>
                
                <div class="text-center mt-3">
                    <p>Already have an account? <a asp-action="Login">Login here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
