﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>9d2f5bcc-3425-4f25-b229-b6426ace669b</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DomainDrivenERP.Identity\DomainDrivenERP.Identity.csproj" />
    <ProjectReference Include="..\DomainDrivenERP.Infrastructure\DomainDrivenERP.Infrastructure.csproj" />
    <ProjectReference Include="..\DomainDrivenERP.Persistence\DomainDrivenERP.Persistence.csproj" />
    <ProjectReference Include="..\DomainDrivenERP.Presentation\DomainDrivenERP.Presentation.csproj" />
  </ItemGroup>

</Project>
