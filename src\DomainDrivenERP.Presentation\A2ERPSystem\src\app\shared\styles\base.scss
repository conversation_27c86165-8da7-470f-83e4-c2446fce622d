$spacing-unit: 0.5rem;
$border-radius: 0.25rem;

// Layout
.page-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  background: var(--tui-base-01);
  border-radius: $border-radius;
  padding: $spacing-unit * 4;
  margin-bottom: $spacing-unit * 4;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-unit * 4;

  h2 {
    margin: 0;
    font-size: 1.75rem;
  }
}

.actions {
  display: flex;
  gap: $spacing-unit * 2;
}

// Utilities
.mb-1 { margin-bottom: $spacing-unit; }
.mb-2 { margin-bottom: $spacing-unit * 2; }
.mb-3 { margin-bottom: $spacing-unit * 3; }
.mb-4 { margin-bottom: $spacing-unit * 4; }

.mt-1 { margin-top: $spacing-unit; }
.mt-2 { margin-top: $spacing-unit * 2; }
.mt-3 { margin-top: $spacing-unit * 3; }
.mt-4 { margin-top: $spacing-unit * 4; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }