/* Custom Cursor Styles - HeyGen-style cursors */

/* Default cursor for html element */
html {
    cursor: url("/images/cursors/default.png") 8 8, default;
}

/* Body and interactive elements */
body {
    cursor: url("/images/cursors/default.png") 8 8, default;
}

/* Pointer cursor for interactive elements */
a,
button,
.btn,
input[type="submit"],
input[type="button"],
.cursor-pointer,
.navbar-brand,
.nav-link {
    cursor: url("/images/cursors/pointer.png") 8 8, pointer !important;
}

/* Hand cursor for clickable elements */
.cursor-hand,
.btn-primary,
.btn-success,
.btn-warning,
.btn-danger,
.btn-info,
.btn-secondary {
    cursor: url("/images/cursors/hand.png") 8 8, pointer !important;
    /* Fallback for missing images */
    cursor: pointer !important;
}

/* Text cursor for input fields */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea,
.cursor-text {
    cursor: url("/images/cursors/text.png") 8 8, text !important;
}

/* Not allowed cursor */
.cursor-not-allowed,
button:disabled,
.btn:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    cursor: url("/images/cursors/not-allowed.png") 8 8, not-allowed !important;
}

/* Loading cursor */
.cursor-loading,
.loading,
form.submitting,
.spinner-border {
    cursor: url("/images/cursors/default.png") 8 8, wait !important;
}

/* Hover effects */
a:hover,
button:hover:not(:disabled),
.btn:hover:not(:disabled) {
    cursor: url("/images/cursors/pointer.png") 8 8, pointer !important;
}

/* Active/Click states */
a:active,
button:active,
.btn:active {
    cursor: url("/images/cursors/hand.png") 8 8, pointer !important;
}

/* Focus states for form elements */
input:focus,
textarea:focus,
select:focus {
    cursor: url("/images/cursors/text.png") 8 8, text !important;
}

/* Special cursor classes */
.cursor-default {
    cursor: url("/images/cursors/default.png") 8 8, default !important;
}

.cursor-pointer {
    cursor: url("/images/cursors/pointer.png") 8 8, pointer !important;
}

.cursor-hand {
    cursor: url("/images/cursors/hand.png") 8 8, pointer !important;
}

.cursor-text {
    cursor: url("/images/cursors/text.png") 8 8, text !important;
}

.cursor-not-allowed {
    cursor: url("/images/cursors/not-allowed.png") 8 8, not-allowed !important;
}

/* Mobile and touch device handling */
@media (max-width: 768px), (hover: none) {
    html,
    body,
    a,
    button,
    .btn,
    input,
    textarea,
    select {
        cursor: auto !important;
    }
}

/* Smooth cursor transitions */
* {
    transition: cursor 0.1s ease;
}

/* Form validation states */
.is-invalid {
    cursor: url("/images/cursors/not-allowed.png") 8 8, not-allowed !important;
}

.is-valid {
    cursor: url("/images/cursors/text.png") 8 8, text !important;
}

/* Loading states */
.btn.loading,
form.submitting .btn {
    cursor: url("/images/cursors/default.png") 8 8, wait !important;
    pointer-events: none;
}

/* Card and interactive components */
.card-header,
.card-title {
    cursor: url("/images/cursors/default.png") 8 8, default !important;
}

.card.clickable,
.list-group-item.clickable {
    cursor: url("/images/cursors/pointer.png") 8 8, pointer !important;
}
