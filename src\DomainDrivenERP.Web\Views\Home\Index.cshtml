﻿@{
    ViewData["Title"] = "Welcome";
}

<div class="text-center fade-in">
    <div class="mb-5">
        <h1 class="display-4 mb-3" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            Welcome to Domain Driven ERP
        </h1>
        <p class="lead mb-4" style="color: var(--text-secondary);">
            A modern, minimal ERP solution built with clean architecture and domain-driven design principles.
        </p>

        @if (!User.Identity.IsAuthenticated)
        {
            <div class="d-flex justify-content-center gap-3 mb-5">
                <a asp-controller="Auth" asp-action="Register" class="btn btn-primary btn-lg">
                    <i class="fas fa-rocket me-2"></i> Get Started
                </a>
                <a asp-controller="Auth" asp-action="Login" class="btn btn-outline btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i> Sign In
                </a>
            </div>
        }
    </div>

    @if (User.Identity.IsAuthenticated)
    {
        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-box fa-3x" style="color: var(--brand-primary);"></i>
                        </div>
                        <h5 class="card-title">Products</h5>
                        <p class="card-text" style="color: var(--text-secondary);">
                            Manage your product catalog with ease
                        </p>
                        <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                            View Products
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-shopping-cart fa-3x" style="color: var(--brand-success);"></i>
                        </div>
                        <h5 class="card-title">Orders</h5>
                        <p class="card-text" style="color: var(--text-secondary);">
                            Track and manage customer orders
                        </p>
                        <a asp-controller="Orders" asp-action="Index" class="btn btn-primary">
                            View Orders
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-users fa-3x" style="color: var(--brand-warning);"></i>
                        </div>
                        <h5 class="card-title">Customers</h5>
                        <p class="card-text" style="color: var(--text-secondary);">
                            Maintain customer relationships
                        </p>
                        <a asp-controller="Customers" asp-action="Index" class="btn btn-primary">
                            View Customers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="card h-100 text-center glass">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-cogs fa-3x" style="color: var(--brand-primary);"></i>
                        </div>
                        <h5 class="card-title">Modern Architecture</h5>
                        <p class="card-text" style="color: var(--text-secondary);">
                            Built with clean architecture and CQRS patterns
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center glass">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-mobile-alt fa-3x" style="color: var(--brand-success);"></i>
                        </div>
                        <h5 class="card-title">Responsive Design</h5>
                        <p class="card-text" style="color: var(--text-secondary);">
                            Works seamlessly on all devices
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center glass">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-shield-alt fa-3x" style="color: var(--brand-warning);"></i>
                        </div>
                        <h5 class="card-title">Secure & Reliable</h5>
                        <p class="card-text" style="color: var(--text-secondary);">
                            Enterprise-grade security and performance
                        </p>
                    </div>
                </div>
            </div>
        </div>
    }

    <div class="mt-5 p-4" style="background: var(--bg-secondary); border-radius: var(--radius-lg);">
        <h3 class="mb-3">Theme Switcher</h3>
        <p style="color: var(--text-secondary);" class="mb-3">
            Toggle between light and dark modes using the switch in the navigation bar, or press <kbd>Ctrl+Shift+T</kbd>
        </p>
        <div class="d-flex justify-content-center">
            <div class="theme-toggle" onclick="window.themeSwitcher?.toggleTheme()"></div>
        </div>
    </div>
</div>
