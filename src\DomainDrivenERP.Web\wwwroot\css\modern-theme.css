/* Modern Minimal Theme with Dark/Light Mode Support */

/* CSS Custom Properties for Theme Variables */
:root {
  /* Light Mode Colors - Ultra Modern */
  --bg-primary: #fefefe;
  --bg-secondary: #f7f8fc;
  --bg-tertiary: #eef1f7;
  --bg-accent: #ffffff;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow: rgba(15, 23, 42, 0.04);
  --shadow-md: rgba(15, 23, 42, 0.08);
  --shadow-lg: rgba(15, 23, 42, 0.12);
  --shadow-hover: rgba(15, 23, 42, 0.16);

  /* Brand Colors - Modern Palette */
  --brand-primary: #6366f1;
  --brand-primary-hover: #4f46e5;
  --brand-primary-light: rgba(99, 102, 241, 0.1);
  --brand-success: #059669;
  --brand-warning: #d97706;
  --brand-danger: #dc2626;
  --brand-info: #0284c7;

  /* Gradients - Subtle and Modern */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --gradient-success: linear-gradient(135deg, #059669 0%, #10b981 100%);
  --gradient-surface: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);

  /* Spacing - Consistent Scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius - Modern Rounded */
  --radius-xs: 0.25rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;

  /* Transitions - Smooth */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Colors - Ultra Modern */
[data-theme="dark"] {
  --bg-primary: #0a0a0b;
  --bg-secondary: #111113;
  --bg-tertiary: #1a1a1d;
  --bg-accent: #18181b;
  --text-primary: #fafafa;
  --text-secondary: #a1a1aa;
  --text-muted: #71717a;
  --border-color: #27272a;
  --border-light: #3f3f46;
  --shadow: rgba(0, 0, 0, 0.3);
  --shadow-md: rgba(0, 0, 0, 0.4);
  --shadow-lg: rgba(0, 0, 0, 0.5);
  --shadow-hover: rgba(0, 0, 0, 0.6);
  --gradient-surface: linear-gradient(145deg, #18181b 0%, #0a0a0b 100%);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  font-weight: 400;
  transition: background-color var(--transition-normal), color var(--transition-normal);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Typography - Modern Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 { font-size: clamp(2rem, 5vw, 3.5rem); font-weight: 800; }
h2 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.75rem); }
h5 { font-size: clamp(1.125rem, 2vw, 1.5rem); }
h6 { font-size: clamp(1rem, 1.5vw, 1.25rem); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.7;
}

.lead {
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* Links */
a {
  color: var(--brand-primary);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
}

a:hover {
  color: var(--brand-primary-hover);
}

a:focus {
  outline: 2px solid var(--brand-primary-light);
  outline-offset: 2px;
}

/* Buttons - Ultra Modern */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
  backdrop-filter: blur(10px);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--brand-primary-light);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(99, 102, 241, 0.35);
}

.btn-secondary {
  background: var(--bg-accent);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px var(--shadow);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px var(--shadow-md);
}

.btn-outline {
  background: transparent;
  color: var(--brand-primary);
  border: 2px solid var(--brand-primary);
  backdrop-filter: blur(10px);
}

.btn-outline:hover:not(:disabled) {
  background: var(--brand-primary);
  color: white;
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 0.75rem;
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: 1rem;
  border-radius: var(--radius-xl);
}

/* Cards - Ultra Modern */
.card {
  background: var(--bg-accent);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: 0 1px 3px var(--shadow), 0 1px 2px var(--shadow);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
  opacity: 0.5;
}

.card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px var(--shadow-lg), 0 8px 16px var(--shadow-md);
  border-color: var(--brand-primary);
}

.card-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: var(--gradient-surface);
  position: relative;
}

.card-body {
  padding: var(--spacing-xl);
  position: relative;
}

.card-footer {
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.card-text {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Glass Card Variant */
.card.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .card.glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Forms */
.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

/* Navigation - Ultra Modern */
.navbar {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid var(--border-light);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all var(--transition-normal);
  padding: var(--spacing-md) 0;
}

[data-theme="dark"] .navbar {
  background: rgba(10, 10, 11, 0.8);
}

.navbar-brand {
  font-weight: 800;
  font-size: 1.5rem;
  color: var(--text-primary) !important;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.navbar-brand i {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-link {
  color: var(--text-secondary) !important;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-lg) !important;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
  text-decoration: none;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: all var(--transition-fast);
  transform: translateX(-50%);
  border-radius: 1px;
}

.nav-link:hover {
  color: var(--brand-primary) !important;
  background-color: var(--brand-primary-light);
  transform: translateY(-1px);
}

.nav-link:hover::before {
  width: 80%;
}

.nav-link.active {
  color: var(--brand-primary) !important;
  background-color: var(--brand-primary-light);
  font-weight: 600;
}

.nav-link.active::before {
  width: 80%;
}

/* Theme Toggle Button - Ultra Modern */
.theme-toggle {
  position: relative;
  width: 56px;
  height: 28px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 14px;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: inset 0 2px 4px var(--shadow);
  overflow: hidden;
}

.theme-toggle::before {
  content: '☀️';
  position: absolute;
  top: 50%;
  left: 6px;
  transform: translateY(-50%);
  font-size: 12px;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.theme-toggle::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background: linear-gradient(145deg, #ffffff, #f0f0f0);
  border-radius: 50%;
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

[data-theme="dark"] .theme-toggle {
  background: var(--bg-tertiary);
}

[data-theme="dark"] .theme-toggle::before {
  content: '🌙';
  left: auto;
  right: 6px;
}

[data-theme="dark"] .theme-toggle::after {
  transform: translateX(28px);
  background: linear-gradient(145deg, #4a5568, #2d3748);
}

.theme-toggle:hover {
  transform: scale(1.05);
  box-shadow: inset 0 2px 4px var(--shadow), 0 4px 12px var(--shadow-md);
}

/* Modern Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }
.d-grid { display: grid; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
.gap-5 { gap: var(--spacing-xl); }

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* Modern Effects */
.blur { backdrop-filter: blur(10px); }
.blur-lg { backdrop-filter: blur(20px); }

.shadow-sm { box-shadow: 0 1px 2px var(--shadow); }
.shadow { box-shadow: 0 4px 6px var(--shadow-md); }
.shadow-lg { box-shadow: 0 10px 15px var(--shadow-lg); }
.shadow-xl { box-shadow: 0 20px 25px var(--shadow-hover); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }

.border { border: 1px solid var(--border-color); }
.border-light { border: 1px solid var(--border-light); }

.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }
.bg-accent { background: var(--bg-accent); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

/* Modern Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
  50% { box-shadow: 0 0 30px rgba(99, 102, 241, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.fade-in {
  animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in {
  animation: slideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.float {
  animation: float 3s ease-in-out infinite;
}

.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Staggered animations */
.fade-in:nth-child(1) { animation-delay: 0.1s; }
.fade-in:nth-child(2) { animation-delay: 0.2s; }
.fade-in:nth-child(3) { animation-delay: 0.3s; }
.fade-in:nth-child(4) { animation-delay: 0.4s; }

/* Floating Labels */
.floating-label {
  position: absolute;
  top: 50%;
  left: var(--spacing-md);
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
  transition: all var(--transition-fast);
  background-color: var(--bg-primary);
  padding: 0 var(--spacing-xs);
}

.floating-label.floating {
  top: 0;
  font-size: 0.75rem;
  color: var(--brand-primary);
}

.form-group {
  position: relative;
}

.form-group.focused .floating-label {
  color: var(--brand-primary);
}

/* Ripple Effect */
.btn {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Loading Button State */
.btn.loading {
  position: relative;
  color: transparent !important;
}

.btn.loading:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Validation States */
.form-control.is-valid {
  border-color: var(--brand-success);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-control.is-invalid {
  border-color: var(--brand-danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Glass Effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }

  .theme-toggle {
    width: 50px;
    height: 25px;
  }

  .theme-toggle:before {
    width: 19px;
    height: 19px;
  }

  [data-theme="dark"] .theme-toggle:before {
    transform: translateX(23px);
  }
}
