{"name": "a2-erpsystem", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:A2ERPSystem": "node dist/a2-erpsystem/server/server.mjs"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.4", "@ng-web-apis/common": "^4.11.1", "@taiga-ui/addon-charts": "^4.30.0", "@taiga-ui/addon-mobile": "^4.30.0", "@taiga-ui/addon-table": "^4.30.0", "@taiga-ui/cdk": "^4.30.0", "@taiga-ui/core": "^4.30.0", "@taiga-ui/icons": "^4.30.0", "@taiga-ui/kit": "^4.30.0", "@taiga-ui/styles": "^4.30.0", "@tinkoff/ng-dompurify": "^4.0.0", "@tinkoff/ng-polymorpheus": "^4.3.0", "express": "^4.18.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.4", "@angular/cli": "^19.2.4", "@angular/compiler-cli": "^19.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}