/* Custom Cursor Styles - Modern CSS Cursors with SVG Data URIs */

/* Create custom cursor using CSS and data URIs */
:root {
    --cursor-default: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath d='M2 2l4 12 2-5 5-2z' fill='%23000' stroke='%23fff' stroke-width='1'/%3E%3C/svg%3E") 2 2, auto;
    --cursor-pointer: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath d='M6 2c0-1 1-2 2-2s2 1 2 2v6h1c1 0 2 1 2 2v4c0 1-1 2-2 2H5c-1 0-2-1-2-2v-4c0-1 1-2 2-2h1V2z' fill='%23667eea' stroke='%23fff' stroke-width='1'/%3E%3C/svg%3E") 8 2, pointer;
    --cursor-text: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath d='M8 2v12M6 2h4M6 14h4' stroke='%23667eea' stroke-width='2' fill='none'/%3E%3C/svg%3E") 8 8, text;
    --cursor-not-allowed: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Ccircle cx='8' cy='8' r='6' fill='none' stroke='%23ef4444' stroke-width='2'/%3E%3Cpath d='M4 4l8 8' stroke='%23ef4444' stroke-width='2'/%3E%3C/svg%3E") 8 8, not-allowed;
}

/* Dark mode cursor colors */
[data-theme="dark"] {
    --cursor-default: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath d='M2 2l4 12 2-5 5-2z' fill='%23fff' stroke='%23000' stroke-width='1'/%3E%3C/svg%3E") 2 2, auto;
    --cursor-pointer: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath d='M6 2c0-1 1-2 2-2s2 1 2 2v6h1c1 0 2 1 2 2v4c0 1-1 2-2 2H5c-1 0-2-1-2-2v-4c0-1 1-2 2-2h1V2z' fill='%23818cf8' stroke='%23000' stroke-width='1'/%3E%3C/svg%3E") 8 2, pointer;
    --cursor-text: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath d='M8 2v12M6 2h4M6 14h4' stroke='%23818cf8' stroke-width='2' fill='none'/%3E%3C/svg%3E") 8 8, text;
    --cursor-not-allowed: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Ccircle cx='8' cy='8' r='6' fill='none' stroke='%23f87171' stroke-width='2'/%3E%3Cpath d='M4 4l8 8' stroke='%23f87171' stroke-width='2'/%3E%3C/svg%3E") 8 8, not-allowed;
}

/* Apply custom cursors */
html, body {
    cursor: var(--cursor-default);
}

/* Pointer cursor for interactive elements */
a,
button,
.btn,
input[type="submit"],
input[type="button"],
.cursor-pointer,
.navbar-brand,
.nav-link,
.card.clickable,
.theme-toggle {
    cursor: var(--cursor-pointer) !important;
}

/* Text cursor for input fields */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="number"],
textarea,
.cursor-text {
    cursor: var(--cursor-text) !important;
}

/* Not allowed cursor */
.cursor-not-allowed,
button:disabled,
.btn:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    cursor: var(--cursor-not-allowed) !important;
}

/* Loading cursor - use default with animation */
.cursor-loading,
.loading,
form.submitting,
.spinner-border {
    cursor: var(--cursor-default) !important;
}

/* Hover effects - enhanced pointer */
a:hover,
button:hover:not(:disabled),
.btn:hover:not(:disabled),
.nav-link:hover {
    cursor: var(--cursor-pointer) !important;
    transition: all 0.2s ease;
}

/* Active/Click states */
a:active,
button:active,
.btn:active {
    cursor: var(--cursor-pointer) !important;
}

/* Focus states for form elements */
input:focus,
textarea:focus,
select:focus {
    cursor: var(--cursor-text) !important;
}

/* Special cursor classes */
.cursor-default {
    cursor: var(--cursor-default) !important;
}

.cursor-pointer {
    cursor: var(--cursor-pointer) !important;
}

.cursor-hand {
    cursor: var(--cursor-pointer) !important;
}

.cursor-text {
    cursor: var(--cursor-text) !important;
}

.cursor-not-allowed {
    cursor: var(--cursor-not-allowed) !important;
}

/* Dropdown and menu items */
.dropdown-item {
    cursor: var(--cursor-pointer) !important;
}

.dropdown-item:hover {
    cursor: var(--cursor-pointer) !important;
}

/* Card hover effects */
.card:hover {
    cursor: var(--cursor-default);
}

.card.clickable:hover {
    cursor: var(--cursor-pointer) !important;
}

/* Mobile and touch device handling */
@media (max-width: 768px), (hover: none) {
    :root {
        --cursor-default: auto;
        --cursor-pointer: auto;
        --cursor-text: auto;
        --cursor-not-allowed: auto;
    }

    html,
    body,
    a,
    button,
    .btn,
    input,
    textarea,
    select {
        cursor: auto !important;
    }
}

/* Smooth cursor transitions */
* {
    transition: cursor 0.1s ease;
}

/* Form validation states */
.is-invalid {
    cursor: var(--cursor-not-allowed) !important;
}

.is-valid {
    cursor: var(--cursor-text) !important;
}

/* Loading states */
.btn.loading,
form.submitting .btn {
    cursor: var(--cursor-default) !important;
    pointer-events: none;
}

/* Card and interactive components */
.card-header,
.card-title {
    cursor: var(--cursor-default) !important;
}

.card.clickable,
.list-group-item.clickable {
    cursor: var(--cursor-pointer) !important;
}

/* Additional interactive elements */
.badge,
.alert .btn-close,
.modal-header .btn-close {
    cursor: var(--cursor-pointer) !important;
}

/* Form elements with custom styling */
.form-check-input,
.form-check-label {
    cursor: var(--cursor-pointer) !important;
}

/* Navigation elements */
.breadcrumb-item a,
.pagination .page-link {
    cursor: var(--cursor-pointer) !important;
}
