﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\package.json" />
    <Content Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\_metadata.json" />
    <Content Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\package.json" />
    <Content Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\_metadata.json" />
    <Content Include="A2ERPSystem\.vscode\extensions.json" />
    <Content Include="A2ERPSystem\.vscode\launch.json" />
    <Content Include="A2ERPSystem\.vscode\tasks.json" />
    <Content Include="A2ERPSystem\node_modules\encoding\node_modules\iconv-lite\.idea\codeStyles\codeStyleConfig.xml" />
    <Content Include="A2ERPSystem\node_modules\encoding\node_modules\iconv-lite\.idea\codeStyles\Project.xml" />
    <Content Include="A2ERPSystem\node_modules\encoding\node_modules\iconv-lite\.idea\inspectionProfiles\Project_Default.xml" />
    <Content Include="A2ERPSystem\node_modules\encoding\node_modules\iconv-lite\.idea\modules.xml" />
    <Content Include="A2ERPSystem\node_modules\encoding\node_modules\iconv-lite\.idea\vcs.xml" />
    <Content Include="A2ERPSystem\node_modules\is-what\.vscode\settings.json" />
    <Content Include="A2ERPSystem\node_modules\needle\node_modules\iconv-lite\.idea\codeStyles\codeStyleConfig.xml" />
    <Content Include="A2ERPSystem\node_modules\needle\node_modules\iconv-lite\.idea\codeStyles\Project.xml" />
    <Content Include="A2ERPSystem\node_modules\needle\node_modules\iconv-lite\.idea\inspectionProfiles\Project_Default.xml" />
    <Content Include="A2ERPSystem\node_modules\needle\node_modules\iconv-lite\.idea\modules.xml" />
    <Content Include="A2ERPSystem\node_modules\needle\node_modules\iconv-lite\.idea\vcs.xml" />
    <Content Include="A2ERPSystem\node_modules\ordered-binary\.idea\modules.xml" />
    <Content Include="A2ERPSystem\node_modules\ordered-binary\.nyc_output\5b6bd808-0399-4ee6-9395-344688761dfa.json" />
    <Content Include="A2ERPSystem\node_modules\ordered-binary\.nyc_output\processinfo\5b6bd808-0399-4ee6-9395-344688761dfa.json" />
    <Content Include="A2ERPSystem\node_modules\ordered-binary\.nyc_output\processinfo\index.json" />
    <Content Include="A2ERPSystem\node_modules\source-map-loader\node_modules\iconv-lite\.idea\codeStyles\codeStyleConfig.xml" />
    <Content Include="A2ERPSystem\node_modules\source-map-loader\node_modules\iconv-lite\.idea\codeStyles\Project.xml" />
    <Content Include="A2ERPSystem\node_modules\source-map-loader\node_modules\iconv-lite\.idea\inspectionProfiles\Project_Default.xml" />
    <Content Include="A2ERPSystem\node_modules\source-map-loader\node_modules\iconv-lite\.idea\modules.xml" />
    <Content Include="A2ERPSystem\node_modules\source-map-loader\node_modules\iconv-lite\.idea\vcs.xml" />
    <Content Include="A2ERPSystem\node_modules\typed-assert\.vscode\extensions.json" />
    <Content Include="A2ERPSystem\node_modules\typed-assert\.vscode\settings.json" />
    <Content Include="A2ERPSystem\node_modules\typed-assert\.vscode\terminals.json" />
  </ItemGroup>

  <ItemGroup>
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\.tsbuildinfo" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_common.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_common.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_common_http.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_common_http.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_core.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_core.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_platform-browser.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_platform-browser.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_platform-browser_animations.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_platform-browser_animations.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_router.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\%40angular_router.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\chunk-7UTSPVKT.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\chunk-7UTSPVKT.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\chunk-DFLTQKNZ.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\chunk-DFLTQKNZ.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\chunk-IYD4D2JK.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\chunk-IYD4D2JK.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\chunk-VEFH4ENC.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps\chunk-VEFH4ENC.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_common.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_common.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_common_http.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_common_http.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_core.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_core.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_platform-browser.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_platform-browser.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_platform-browser_animations.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_platform-browser_animations.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_platform-server.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_platform-server.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_router.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_router.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_ssr.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_ssr.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_ssr_node.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\%40angular_ssr_node.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-2XII6U4I.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-2XII6U4I.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-AH4HXNGI.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-AH4HXNGI.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-EWETOJTL.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-EWETOJTL.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-IVFXCVVG.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-IVFXCVVG.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-JZYAYK5E.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-JZYAYK5E.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-RF44RIZN.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-RF44RIZN.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-RMUIWRBI.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-RMUIWRBI.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-YHCV7DAQ.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\chunk-YHCV7DAQ.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\express.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\express.js.map" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\xhr2-TXIMV6CV.js" />
    <None Include="A2ERPSystem\.angular\cache\19.2.4\A2ERPSystem\vite\deps_ssr\xhr2-TXIMV6CV.js.map" />
    <None Include="A2ERPSystem\node_modules\%40angular\build\node_modules\.bin\vite" />
    <None Include="A2ERPSystem\node_modules\%40angular\build\node_modules\.bin\vite.cmd" />
    <None Include="A2ERPSystem\node_modules\%40angular\build\node_modules\.bin\vite.ps1" />
    <None Include="A2ERPSystem\node_modules\%40angular\compiler-cli\node_modules\%40babel\core\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\%40angular\compiler-cli\node_modules\%40babel\core\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\%40angular\compiler-cli\node_modules\%40babel\core\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\%40babel\core\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\%40babel\core\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\%40babel\core\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-compilation-targets\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-compilation-targets\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-compilation-targets\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-create-class-features-plugin\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-create-class-features-plugin\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-create-class-features-plugin\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-create-regexp-features-plugin\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-create-regexp-features-plugin\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\%40babel\helper-create-regexp-features-plugin\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\%40babel\plugin-transform-runtime\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\%40babel\plugin-transform-runtime\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\%40babel\plugin-transform-runtime\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\%40babel\preset-env\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\%40babel\preset-env\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\%40babel\preset-env\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\git\node_modules\.bin\node-which" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\git\node_modules\.bin\node-which.cmd" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\git\node_modules\.bin\node-which.ps1" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\package-json\node_modules\.bin\glob" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\package-json\node_modules\.bin\glob.cmd" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\package-json\node_modules\.bin\glob.ps1" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\package-json\node_modules\brace-expansion\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\promise-spawn\node_modules\.bin\node-which" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\promise-spawn\node_modules\.bin\node-which.cmd" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\promise-spawn\node_modules\.bin\node-which.ps1" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\run-script\node_modules\.bin\node-which" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\run-script\node_modules\.bin\node-which.cmd" />
    <None Include="A2ERPSystem\node_modules\%40npmcli\run-script\node_modules\.bin\node-which.ps1" />
    <None Include="A2ERPSystem\node_modules\%40parcel\watcher\node_modules\.bin\detect-libc" />
    <None Include="A2ERPSystem\node_modules\%40parcel\watcher\node_modules\.bin\detect-libc.cmd" />
    <None Include="A2ERPSystem\node_modules\%40parcel\watcher\node_modules\.bin\detect-libc.ps1" />
    <None Include="A2ERPSystem\node_modules\%40ts-morph\common\node_modules\.bin\mkdirp" />
    <None Include="A2ERPSystem\node_modules\%40ts-morph\common\node_modules\.bin\mkdirp.cmd" />
    <None Include="A2ERPSystem\node_modules\%40ts-morph\common\node_modules\.bin\mkdirp.ps1" />
    <None Include="A2ERPSystem\node_modules\%40ts-morph\common\node_modules\brace-expansion\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\%40tufjs\models\node_modules\brace-expansion\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\.bin\acorn" />
    <None Include="A2ERPSystem\node_modules\.bin\acorn.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\acorn.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\ansi-html" />
    <None Include="A2ERPSystem\node_modules\.bin\ansi-html.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\ansi-html.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\autoprefixer" />
    <None Include="A2ERPSystem\node_modules\.bin\autoprefixer.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\autoprefixer.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\browserslist" />
    <None Include="A2ERPSystem\node_modules\.bin\browserslist.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\browserslist.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\cssesc" />
    <None Include="A2ERPSystem\node_modules\.bin\cssesc.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\cssesc.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\download-lmdb-prebuilds" />
    <None Include="A2ERPSystem\node_modules\.bin\download-lmdb-prebuilds.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\download-lmdb-prebuilds.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\download-msgpackr-prebuilds" />
    <None Include="A2ERPSystem\node_modules\.bin\download-msgpackr-prebuilds.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\download-msgpackr-prebuilds.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\errno" />
    <None Include="A2ERPSystem\node_modules\.bin\errno.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\errno.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\esbuild" />
    <None Include="A2ERPSystem\node_modules\.bin\esbuild.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\esbuild.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\flat" />
    <None Include="A2ERPSystem\node_modules\.bin\flat.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\flat.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\image-size" />
    <None Include="A2ERPSystem\node_modules\.bin\image-size.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\image-size.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\installed-package-contents" />
    <None Include="A2ERPSystem\node_modules\.bin\installed-package-contents.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\installed-package-contents.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\is-docker" />
    <None Include="A2ERPSystem\node_modules\.bin\is-docker.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\is-docker.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\is-inside-container" />
    <None Include="A2ERPSystem\node_modules\.bin\is-inside-container.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\is-inside-container.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\jiti" />
    <None Include="A2ERPSystem\node_modules\.bin\jiti.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\jiti.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\js-yaml" />
    <None Include="A2ERPSystem\node_modules\.bin\js-yaml.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\js-yaml.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\jsesc" />
    <None Include="A2ERPSystem\node_modules\.bin\jsesc.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\jsesc.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\json5" />
    <None Include="A2ERPSystem\node_modules\.bin\json5.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\json5.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\karma" />
    <None Include="A2ERPSystem\node_modules\.bin\karma.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\karma.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\lessc" />
    <None Include="A2ERPSystem\node_modules\.bin\lessc.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\lessc.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\mime" />
    <None Include="A2ERPSystem\node_modules\.bin\mime.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\mime.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\mkdirp" />
    <None Include="A2ERPSystem\node_modules\.bin\mkdirp.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\mkdirp.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\multicast-dns" />
    <None Include="A2ERPSystem\node_modules\.bin\multicast-dns.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\multicast-dns.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\nanoid" />
    <None Include="A2ERPSystem\node_modules\.bin\nanoid.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\nanoid.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\needle" />
    <None Include="A2ERPSystem\node_modules\.bin\needle.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\needle.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\ng" />
    <None Include="A2ERPSystem\node_modules\.bin\ng-xi18n" />
    <None Include="A2ERPSystem\node_modules\.bin\ng-xi18n.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\ng-xi18n.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\ng.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\ng.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\ngc" />
    <None Include="A2ERPSystem\node_modules\.bin\ngc.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\ngc.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\ngcc" />
    <None Include="A2ERPSystem\node_modules\.bin\ngcc.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\ngcc.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages-optional" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages-optional.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages-optional.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages-test" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages-test.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages-test.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp-build-optional-packages.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\node-gyp.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\nopt" />
    <None Include="A2ERPSystem\node_modules\.bin\nopt.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\nopt.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\pacote" />
    <None Include="A2ERPSystem\node_modules\.bin\pacote.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\pacote.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\parser" />
    <None Include="A2ERPSystem\node_modules\.bin\parser.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\parser.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\regjsparser" />
    <None Include="A2ERPSystem\node_modules\.bin\regjsparser.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\regjsparser.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\resolve" />
    <None Include="A2ERPSystem\node_modules\.bin\resolve.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\resolve.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\rimraf" />
    <None Include="A2ERPSystem\node_modules\.bin\rimraf.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\rimraf.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\rollup" />
    <None Include="A2ERPSystem\node_modules\.bin\rollup.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\rollup.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\sass" />
    <None Include="A2ERPSystem\node_modules\.bin\sass.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\sass.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\terser" />
    <None Include="A2ERPSystem\node_modules\.bin\terser.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\terser.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\tree-kill" />
    <None Include="A2ERPSystem\node_modules\.bin\tree-kill.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\tree-kill.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\tsc" />
    <None Include="A2ERPSystem\node_modules\.bin\tsc.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\tsc.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\tsserver" />
    <None Include="A2ERPSystem\node_modules\.bin\tsserver.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\tsserver.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\ua-parser-js" />
    <None Include="A2ERPSystem\node_modules\.bin\ua-parser-js.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\ua-parser-js.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\update-browserslist-db" />
    <None Include="A2ERPSystem\node_modules\.bin\update-browserslist-db.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\update-browserslist-db.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\uuid" />
    <None Include="A2ERPSystem\node_modules\.bin\uuid.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\uuid.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\vite" />
    <None Include="A2ERPSystem\node_modules\.bin\vite.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\vite.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\webpack" />
    <None Include="A2ERPSystem\node_modules\.bin\webpack-dev-server" />
    <None Include="A2ERPSystem\node_modules\.bin\webpack-dev-server.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\webpack-dev-server.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\webpack.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\webpack.ps1" />
    <None Include="A2ERPSystem\node_modules\.bin\which" />
    <None Include="A2ERPSystem\node_modules\.bin\which.cmd" />
    <None Include="A2ERPSystem\node_modules\.bin\which.ps1" />
    <None Include="A2ERPSystem\node_modules\babel-plugin-polyfill-corejs2\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\babel-plugin-polyfill-corejs2\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\babel-plugin-polyfill-corejs2\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\balanced-match\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\cacache\node_modules\.bin\glob" />
    <None Include="A2ERPSystem\node_modules\cacache\node_modules\.bin\glob.cmd" />
    <None Include="A2ERPSystem\node_modules\cacache\node_modules\.bin\glob.ps1" />
    <None Include="A2ERPSystem\node_modules\cacache\node_modules\.bin\mkdirp" />
    <None Include="A2ERPSystem\node_modules\cacache\node_modules\.bin\mkdirp.cmd" />
    <None Include="A2ERPSystem\node_modules\cacache\node_modules\.bin\mkdirp.ps1" />
    <None Include="A2ERPSystem\node_modules\cacache\node_modules\brace-expansion\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\call-bind-apply-helpers\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\call-bound\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\cross-spawn\node_modules\.bin\node-which" />
    <None Include="A2ERPSystem\node_modules\cross-spawn\node_modules\.bin\node-which.cmd" />
    <None Include="A2ERPSystem\node_modules\cross-spawn\node_modules\.bin\node-which.ps1" />
    <None Include="A2ERPSystem\node_modules\dunder-proto\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\encoding\node_modules\iconv-lite\.github\dependabot.yml" />
    <None Include="A2ERPSystem\node_modules\encoding\node_modules\iconv-lite\.idea\iconv-lite.iml" />
    <None Include="A2ERPSystem\node_modules\ent\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\es-define-property\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\es-errors\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\es-object-atoms\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\events\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\fast-uri\.github\.stale.yml" />
    <None Include="A2ERPSystem\node_modules\fast-uri\.github\dependabot.yml" />
    <None Include="A2ERPSystem\node_modules\fast-uri\.github\tests_checker.yml" />
    <None Include="A2ERPSystem\node_modules\fast-uri\.github\workflows\ci.yml" />
    <None Include="A2ERPSystem\node_modules\fast-uri\.github\workflows\package-manager-ci.yml" />
    <None Include="A2ERPSystem\node_modules\fastq\.github\dependabot.yml" />
    <None Include="A2ERPSystem\node_modules\fastq\.github\workflows\ci.yml" />
    <None Include="A2ERPSystem\node_modules\function-bind\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\function-bind\.github\SECURITY.md" />
    <None Include="A2ERPSystem\node_modules\get-intrinsic\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\get-proto\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\gopd\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\has-symbols\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\has-tostringtag\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\hasown\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\ignore-walk\node_modules\brace-expansion\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\is-what\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\json-schema-traverse\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\json-schema-traverse\.github\workflows\build.yml" />
    <None Include="A2ERPSystem\node_modules\json-schema-traverse\.github\workflows\publish.yml" />
    <None Include="A2ERPSystem\node_modules\karma-coverage\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\karma-coverage\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\karma-coverage\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\less\node_modules\.bin\mime" />
    <None Include="A2ERPSystem\node_modules\less\node_modules\.bin\mime.cmd" />
    <None Include="A2ERPSystem\node_modules\less\node_modules\.bin\mime.ps1" />
    <None Include="A2ERPSystem\node_modules\less\node_modules\.bin\semver" />
    <None Include="A2ERPSystem\node_modules\less\node_modules\.bin\semver.cmd" />
    <None Include="A2ERPSystem\node_modules\less\node_modules\.bin\semver.ps1" />
    <None Include="A2ERPSystem\node_modules\lmdb\dependencies\lz4\lib\dll\example\fullbench-dll.sln" />
    <None Include="A2ERPSystem\node_modules\lmdb\dependencies\lz4\lib\dll\example\fullbench-dll.vcxproj" />
    <None Include="A2ERPSystem\node_modules\math-intrinsics\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\minimist\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\minizlib\node_modules\.bin\glob" />
    <None Include="A2ERPSystem\node_modules\minizlib\node_modules\.bin\glob.cmd" />
    <None Include="A2ERPSystem\node_modules\minizlib\node_modules\.bin\glob.ps1" />
    <None Include="A2ERPSystem\node_modules\minizlib\node_modules\.bin\rimraf" />
    <None Include="A2ERPSystem\node_modules\minizlib\node_modules\.bin\rimraf.cmd" />
    <None Include="A2ERPSystem\node_modules\minizlib\node_modules\.bin\rimraf.ps1" />
    <None Include="A2ERPSystem\node_modules\minizlib\node_modules\brace-expansion\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\needle\.github\workflows\nodejs.yml" />
    <None Include="A2ERPSystem\node_modules\needle\node_modules\iconv-lite\.github\dependabot.yml" />
    <None Include="A2ERPSystem\node_modules\needle\node_modules\iconv-lite\.idea\iconv-lite.iml" />
    <None Include="A2ERPSystem\node_modules\ng-morph\node_modules\brace-expansion\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\glob" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\glob.cmd" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\glob.ps1" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\mkdirp" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\mkdirp.cmd" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\mkdirp.ps1" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\node-which" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\node-which.cmd" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\.bin\node-which.ps1" />
    <None Include="A2ERPSystem\node_modules\node-gyp\node_modules\brace-expansion\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\object-inspect\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\ordered-binary\.idea\ordered-binary.iml" />
    <None Include="A2ERPSystem\node_modules\path-browserify\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\qs\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\regjsparser\node_modules\.bin\jsesc" />
    <None Include="A2ERPSystem\node_modules\regjsparser\node_modules\.bin\jsesc.cmd" />
    <None Include="A2ERPSystem\node_modules\regjsparser\node_modules\.bin\jsesc.ps1" />
    <None Include="A2ERPSystem\node_modules\resolve\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\reusify\.github\dependabot.yml" />
    <None Include="A2ERPSystem\node_modules\reusify\.github\workflows\ci.yml" />
    <None Include="A2ERPSystem\node_modules\rfdc\.github\workflows\ci.yml" />
    <None Include="A2ERPSystem\node_modules\safe-regex-test\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\send\node_modules\.bin\mime" />
    <None Include="A2ERPSystem\node_modules\send\node_modules\.bin\mime.cmd" />
    <None Include="A2ERPSystem\node_modules\send\node_modules\.bin\mime.ps1" />
    <None Include="A2ERPSystem\node_modules\shell-quote\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\side-channel-list\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\side-channel-map\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\side-channel-weakmap\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\side-channel\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\source-map-loader\node_modules\iconv-lite\.github\dependabot.yml" />
    <None Include="A2ERPSystem\node_modules\source-map-loader\node_modules\iconv-lite\.idea\iconv-lite.iml" />
    <None Include="A2ERPSystem\node_modules\supports-preserve-symlinks-flag\.github\FUNDING.yml" />
    <None Include="A2ERPSystem\node_modules\tar\node_modules\.bin\mkdirp" />
    <None Include="A2ERPSystem\node_modules\tar\node_modules\.bin\mkdirp.cmd" />
    <None Include="A2ERPSystem\node_modules\tar\node_modules\.bin\mkdirp.ps1" />
    <None Include="A2ERPSystem\node_modules\wildcard\.github\workflows\build.yml" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DomainDrivenERP.Application\DomainDrivenERP.Application.csproj" />
    <ProjectReference Include="..\DomainDrivenERP.Domain\DomainDrivenERP.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="SonarAnalyzer.CSharp" Version="10.7.0.110445" />
  </ItemGroup>

</Project>
