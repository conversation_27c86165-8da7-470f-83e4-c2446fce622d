﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.14" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.14" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="Quartz" Version="3.14.0" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
    <PackageReference Include="Scrutor" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
	  <ProjectReference Include="..\DomainDrivenERP.Application\DomainDrivenERP.Application.csproj" />
	  <ProjectReference Include="..\DomainDrivenERP.Domain\DomainDrivenERP.Domain.csproj" />
    <ProjectReference Include="..\DomainDrivenERP.Infrastructure\DomainDrivenERP.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="SonarAnalyzer.CSharp" Version="10.7.0.110445" />
  </ItemGroup>

</Project>
