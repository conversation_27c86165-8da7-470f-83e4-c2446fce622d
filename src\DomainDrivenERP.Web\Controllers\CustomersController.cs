using DomainDrivenERP.Web.Models.Customers;
using DomainDrivenERP.Web.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace DomainDrivenERP.Web.Controllers;

[Authorize]
public class CustomersController : Controller
{
    private readonly IApiService _apiService;
    private readonly IAuthService _authService;
    private readonly ILogger<CustomersController> _logger;

    public CustomersController(IApiService apiService, IAuthService authService, ILogger<CustomersController> logger)
    {
        _apiService = apiService;
        _authService = authService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        await SetAuthorizationToken();
        
        // For now, return empty list - we'll implement GetAll customers later
        var customers = new List<Customer>();
        return View(customers);
    }

    public async Task<IActionResult> Details(Guid id)
    {
        await SetAuthorizationToken();
        
        var response = await _apiService.GetAsync<Customer>($"api/v1/customers/{id}");
        
        if (!response.IsSuccess)
        {
            TempData["ErrorMessage"] = response.ErrorMessage;
            return RedirectToAction(nameof(Index));
        }

        return View(response.Data);
    }

    private async Task SetAuthorizationToken()
    {
        var token = await _authService.GetTokenAsync();
        if (!string.IsNullOrEmpty(token))
        {
            _apiService.SetAuthorizationToken(token);
        }
    }
}
