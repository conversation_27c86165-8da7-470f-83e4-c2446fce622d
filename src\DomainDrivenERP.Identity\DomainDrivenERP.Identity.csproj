﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MailKit" Version="4.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.14" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DomainDrivenERP.Application\DomainDrivenERP.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="SonarAnalyzer.CSharp" Version="10.7.0.110445" />
  </ItemGroup>

</Project>
