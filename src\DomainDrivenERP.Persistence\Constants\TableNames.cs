﻿namespace DomainDrivenERP.Persistence.Constants;

internal static class TableNames
{
    internal const string Customers = nameof(Customers);
    internal const string Invoices = nameof(Invoices);
    internal const string OutboxMessages = nameof(OutboxMessages);
    internal const string OutboxMessageConsumers = nameof(OutboxMessageConsumers);
    internal const string Accounts = nameof(Accounts);
    internal const string Journals = nameof(Journals);
    internal const string Transactions = nameof(Transactions);
    internal const string Products = nameof(Products);
    internal const string Categories = nameof(Categories);
    internal const string Orders = nameof(Orders);
    internal const string LineItems = nameof(LineItems);
    internal const string Languages = nameof(Languages);
    internal const string LanguageResources = nameof(LanguageResources);
    internal const string TranslationCaches = nameof(TranslationCaches);
    internal const string TranslationImports = nameof(TranslationImports);
    internal const string TranslationExports = nameof(TranslationExports);
    internal const string LocalizationSettings = nameof(LocalizationSettings);
    internal const string TranslationAudits = nameof(TranslationAudits);
}
