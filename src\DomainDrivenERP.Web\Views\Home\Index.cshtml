﻿@{
    ViewData["Title"] = "Welcome";
}

<div class="text-center">
    <h1 class="display-4">Welcome to Domain Driven ERP</h1>
    <p class="lead">A comprehensive Enterprise Resource Planning system built with modern architecture.</p>

    <div class="row mt-5">
        <div class="col-md-6 offset-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Get Started</h5>
                    <p class="card-text">Sign in to access your ERP dashboard and manage your business operations.</p>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a asp-controller="Auth" asp-action="Login" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                        <a asp-controller="Auth" asp-action="Register" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus"></i> Register
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-5">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-box fa-2x text-primary mb-3"></i>
                    <h5>Product Management</h5>
                    <p class="card-text">Comprehensive inventory and product catalog management.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-shopping-cart fa-2x text-success mb-3"></i>
                    <h5>Order Processing</h5>
                    <p class="card-text">Streamlined order management and fulfillment tracking.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-info mb-3"></i>
                    <h5>Customer Relations</h5>
                    <p class="card-text">Maintain strong customer relationships and data.</p>
                </div>
            </div>
        </div>
    </div>
</div>
